# Firebase
FIREBASE_SERVICE_ACCOUNT={"type":"service_account","project_id":"your-project-id","private_key_id":"your-private-key-id","private_key":"your-private-key","client_email":"*****************","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"your-client-cert-url"}
STORAGE_BUCKET=palas-run-cache

# OpenAI
OPENAI_API_KEY=********************************************************************************************************************************************************************
CAMPAIGN_ANALYSIS_AGENT=asst_8z7v7LffCsuh2Ez9tBtStYbg
DISCOVERY_AGENT=asst_we8mAAj5oluautOVautmiJqn
SEED_INFLUENCER_AGENT=asst_lPjsRbNYLY0eR7mdW3XVSZFq
WEB_ANALYSIS_AGENT=asst_8z7v7LffCsuh2Ez9tBtStYbg
ROI_ANALYSIS_AGENT=asst_8z7v7LffCsuh2Ez9tBtStYbg

# Influencers Club
INFLUENCERS_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************.qpbfGOObJMjLjYob_bzW7VX10SCdZpMVLxBEITIUuZc

# Server
PORT=3000
