# Palas Influencer Intelligence Platform

This project is an implementation of the Palas influencer analysis platform using Firestore for data storage and Google Cloud Run for deployment.

## Project Structure

The project is organized into the following directories:

- `config/`: Configuration files
- `connectors/`: API connectors
- `helpers/`: Helper functions
- `models/`: Data models
- `services/`: Business logic services
- `routes/`: API routes
- `middleware/`: Express middleware
- `utils/`: Utility functions
- `old_implementation/`: Original implementation (unchanged)

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- Firebase project with Firestore enabled
- Google Cloud Storage bucket
- OpenAI API key with access to GPT-4.1
- Influencers Club API key

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example`:
   ```
   cp .env.example .env
   ```
4. Update the `.env` file with your credentials
5. Start the server:
   ```
   npm start
   ```

### Firebase Configuration

The project uses a centralized Firebase configuration located in `config/firebase-config.js`. This configuration is used by both the test script and the production application.

The Firebase credentials are located at:
```
credentials/palas-influencer-intelligence-firebase-adminsdk-fbsvc-7f452c5b1f.json
```

For more details, see [Firebase Configuration](config/FIREBASE-README.md).

### Testing

The project includes a comprehensive test script for the influencer analysis flow:

```
node test-analysis-flow.js <username>
```

This script tests the full analysis pipeline using:
- Sample campaign data from `sample_jsons/campaign_analysis.json`
- Sample influencer discovery data from `sample_jsons/influencer_discovery.json`
- Real influencer data from the Influencers Club API (with 14-day caching)
- Production AI analysis components

For more details, see [Test Script Documentation](TEST-README.md).

## API Endpoints

### Campaigns

- `POST /api/campaigns`: Create a new campaign
- `POST /api/campaigns/brief`: Generate a campaign brief
- `GET /api/campaigns/:campaignId`: Get a campaign
- `PUT /api/campaigns/:campaignId`: Update a campaign

### Influencers

- `POST /api/influencers/enrich`: Enrich an influencer
- `GET /api/influencers/:influencerId`: Get an influencer
- `POST /api/influencers/campaigns/:campaignId`: Add an influencer to a campaign
- `PUT /api/influencers/campaigns/:campaignId/:influencerId/status`: Update an influencer's status in a campaign
- `POST /api/influencers/complete-analysis`: Perform complete influencer analysis (enrich + all analyses + merged result)

### Discovery

- `POST /api/discovery/seed`: Get seed influencers for a campaign
- `POST /api/discovery/discover`: Discover influencers for a campaign
- `POST /api/discovery/campaigns/:campaignId/discover`: Discover influencers for a campaign by ID
- `POST /api/discovery/discover-from-request`: Generate campaign brief and discover influencers in one step

### Analysis

- `POST /api/analysis/web`: Perform web analysis
- `POST /api/analysis/aesthetic`: Perform aesthetic analysis
- `POST /api/analysis/roi`: Perform ROI analysis
- `GET /api/analysis/combined/:campaignId/:influencerId`: Get combined analysis
- `GET /api/analysis/merged/:campaignId/:influencerId`: Get merged analysis
- `GET /api/analysis/client/:clientId/merged`: Get all merged analyses for a client

### Legacy

- `POST /api/analyze`: Legacy endpoint for backward compatibility

## Deployment

This project can be deployed to Google Cloud Run for scalable, containerized deployment. For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

### Cloud Run Benefits

- **Scalability**: Automatically scales based on traffic
- **Cost-effective**: Pay only for the resources you use
- **Managed**: No need to manage infrastructure
- **Secure**: Built-in security features

### Quick Deployment

```bash
# Build and deploy using Cloud Build
gcloud builds submit --config=cloudbuild.yaml
```

## License

This project is proprietary and confidential.
